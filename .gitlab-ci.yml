# CI/CD pipeline for AerOS

image: "ulmerman/aeros-builder:latest"

variables:
  CARGO_HOME: /cache/cargo
  CARGO_TARGET_DIR: /cache/target
  RUSTFLAGS: "-C target-cpu=native"
  PACKAGE_NAME: "aeros"

cache:
  key: "cargo-cache"
  paths:
    - /cache/cargo
    - /cache/target

stages:
  - fmt
  - lint
  - test
  - build
  - upload

before_script:
  - rustc --version && cargo --version
  - mkdir -p /cache/cargo /cache/target

fmt:
  stage: fmt
  tags:
    - docker
  script:
    - cargo fmt --all -- --check
  artifacts:
    paths:
      - .
    expire_in: 1 week
    when: always

lint:
  stage: lint
  tags:
    - docker
  script:
    - cargo clippy --all-targets --all-features -- 

test:
  stage: test
  tags:
    - docker
  script:
    - cargo test --all
  dependencies:
    - fmt

build:
  stage: build
  tags:
    - docker

  script:
    - cargo build --release
  dependencies:
    - test
  artifacts:
    paths:
      - target/release/
    expire_in: 2 weeks
