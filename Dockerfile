FROM rustlang/rust:nightly

LABEL maintainer="<EMAIL>"
LABEL description="Build environment for aeros Rust project"

# Install necessary system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    pkg-config \
    libssl-dev \
    libudev-dev \
    libgstreamer1.0-dev \
    libgstreamer-plugins-base1.0-dev \
    cmake \
    clang \
    git \
    ca-certificates \
    curl \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

RUN rustup component add rustfmt clippy

# Default command: interactive shell
CMD [ "bash" ]
