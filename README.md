# AerOS

AerOS is our main software to control our CanSat. It runs on the
Raspberry Pi CM4 and is written in Rust.

## Features

- State machine for mission control (Initializing, Testing, Waiting, Mission, Landing, Stopping states)
- System statistics monitoring (CPU usage, memory, temperature)
- Thermal camera integration with MLX90640 sensor
- Color camera recording with libcamera
- BME280 environmental sensor (temperature, pressure, humidity)
- GPS tracking with NMEA parsing
- CSV data logging with customizable formats
- Configurable logging system
- XBee communication using custom MAVLink dialect
- Buzzer control for landing phase

## Requirements

- Rust 2024 edition
- Raspberry Pi CM4 (or compatible) for thermal camera functionality
- Linux-based OS (for full system statistics support)
- I2C enabled for thermal camera and BME280 sensor
- XBee module connected via serial port
- GPS module connected via UART

## Installation

Clone the repository with submodules:

```bash
git clone --recursive https://git.aerospace-lab.de/aerosat/software/aeros.git
cd aeros
```

Build the project:

```bash
cargo build --release
```

## Configuration

Create a `config.toml` file in the project root:

```toml
[logging]
level = "info"
path = "./data/logs"

[mission]
path = "./data/mission"

[xbee]
baud_rate = 9600
port = "/dev/ttyUSB0"

[methlink]
system_id = 1

[thermal_camera]
bus = "/dev/i2c-0"
addr = 0x33
frequency = 400000
fps = 2

[system_stats]
cpu_usage_threshold = 50.0
cpu_temperature_threshold = 69.0
memory_usage_threshold = 69.0

[buzzer]
channel = 0
frequency = 2700.0
duty_cycle = 0.5

[color_camera]
width = 640
height = 480
fps = 30

[bme280]
address = 0x76
i2c_bus = "/dev/i2c-2"
fps = 1

[gps]
port = "/dev/ttyAMA0"
baud_rate = 9600
```

## Usage

Run the application:

```bash
cargo run --release
```

## Data Structure

The application creates the following data structure:

```
data/
├── logs/
│   └── aeros-YYYY-MM-DD_HH-MM-SS.log
└── mission-YYYY-MM-DD_HH-MM-SS/
    ├── system_stats.csv
    ├── thermal_camera.csv
    ├── bme280.csv
    └── color_camera.mp4
```

## Architecture

### State Machine

The AerOS mission follows a state machine with the following states:

![State Machine Diagram](docs/state-machine-diagram/state-machine.puml)

## Project Structure

- `src/config.rs` - Configuration structures
- `src/sensors/` - Sensor implementations (thermal camera, BME280, GPS)
- `src/states.rs` - Mission state machine
- `src/xbee/` - XBee communication and MAVLink implementation
- `src/buzzer.rs` - Buzzer control for landing phase
- `src/threading.rs` - Thread management for concurrent operations
- `src/globals.rs` - Global state management

## Testing

Run the test suite:

```bash
cargo test
```

## Documentation

For more detailed documentation, visit our [wiki](https://wiki.aerospace-lab.de/spaces/PROJ/pages/238649405/AerOS).
