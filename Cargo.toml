[package]
name = "aeros"
version = "0.1.0"
edition = "2024"

[features]
default = ["serde"]
mock = ["default"]
legacy_thermal_camera = ["default"]

[dependencies]
chrono = "0.4.42"
csv = "1.3.1"
log = "0.4.28"
serde = { version = "1.0.228", features = ["derive"], optional = true }
serialport = "4.7.3"
simplelog = "0.12.2"
sysinfo = "0.37.1"
systemstat = "0.2.5"
toml = "0.9.7"
csv-writer ={ git="https://git.aerospace-lab.de/manuel.ulmer/csv-writer.git", branch = "master" }
mlx9064x = { git="https://github.com/gnxlxnxx/mlx9064x-rs/", branch="embedded-hal-10" }
rppal = "0.22.1"
linux-embedded-hal = "0.4.0"
anyhow = "1.0.99"
embedded-devices = { version = "0.10.1", features = ["bosch-bme280"] }
bme280 = "0.5.1"
methlink-xbee = { git = "https://git.aerospace-lab.de/manuel.ulmer/methlink-xbee.git", tag = "v0.5.1" }
num-traits = "0.2.19"
mavlink-core = "0.15.0"
mavlink = { version = "0.15.0", features = ["serde", "direct-serial", "std"] }
nmea = "0.7.0"
gstreamer = "0.24.2"

[workspace.metadata.cross.target.aarch64-unknown-linux-gnu]
pre-build = [
    "dpkg --add-architecture $CROSS_DEB_ARCH",
    "apt-get update && apt-get --assume-yes install libssl-dev:$CROSS_DEB_ARCH libudev-dev:$CROSS_DEB_ARCH",
]
