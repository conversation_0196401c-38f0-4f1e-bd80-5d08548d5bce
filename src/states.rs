//! This module contains the state machine for the CanSat.
//!
//! The state machine manages transitions between different operational states
//! of the CanSat during its mission lifecycle, from initialization to landing.

use std::fmt::{Debug, Display, Formatter, Result};
use std::sync::{Arc, Mutex};

/// Represents the different states the CanSat can be in.
#[derive(Debu<PERSON>, <PERSON>lone, Co<PERSON>, PartialEq)]
pub enum CanState {
    /// Initializing state for initializing the CanSat
    Initializing,
    /// Testing state for testing before launch
    Testing,
    /// Initial state before mission begins
    Waiting,
    /// Mission state when CanSat is ascending
    Ascending,
    /// Mission state when CanSat is descending
    Descending,
    /// Landed: Final state when CanSat has landed
    Landed,
    /// Stopping: Final state when CanSat has stopped
    Stopping,
}

impl Display for CanState {
    fn fmt(&self, f: &mut Formatter<'_>) -> Result {
        match self {
            CanState::Initializing => write!(f, "Initializing"),
            CanState::Testing => write!(f, "Testing"),
            CanState::Waiting => write!(f, "Waiting"),
            CanState::Ascending => write!(f, "Ascending"),
            CanState::Descending => write!(f, "Descending"),
            CanState::Landed => write!(f, "Landed"),
            CanState::Stopping => write!(f, "Stopping"),
        }
    }
}

/// Main CanSat state machine that manages state transitions.
#[derive(Debug, Clone)]
pub struct StateMachine {
    /// Current state of the CanSat, wrapped in thread-safe containers
    state: Arc<Mutex<CanState>>,
}

impl Default for StateMachine {
    fn default() -> Self {
        Self::new(CanState::Initializing)
    }
}

impl StateMachine {
    /// Creates a new state machine initialized to the Initializing state.
    pub fn new(first_state: CanState) -> StateMachine {
        StateMachine {
            state: Arc::new(Mutex::new(first_state)),
        }
    }

    /// Transitions from Initializing to Testing state.
    ///
    /// # Errors
    ///
    /// Returns an error if called when not in Initializing state or if
    /// the transition is otherwise invalid.
    pub fn start_testing(&mut self) -> anyhow::Result<()> {
        let mut state = self.state.lock().unwrap();
        match *state {
            CanState::Initializing => {
                *state = CanState::Testing;
                Ok(())
            }
            CanState::Testing => Err(anyhow::anyhow!("Testing already started")),
            _ => Err(anyhow::anyhow!("Invalid state transition")),
        }
    }

    /// Transitions from Testing to Waiting state.
    ///
    /// # Errors
    ///
    /// Returns an error if called when not in Testing state or if
    /// the transition is otherwise invalid.
    pub fn start_waiting(&mut self) -> anyhow::Result<()> {
        let mut state = self.state.lock().unwrap();
        match *state {
            CanState::Testing => {
                *state = CanState::Waiting;
                Ok(())
            }
            CanState::Waiting => Err(anyhow::anyhow!("Waiting already started")),
            _ => Err(anyhow::anyhow!("Invalid state transition")),
        }
    }

    /// Transitions from Waiting to Ascending state.
    ///
    /// # Errors
    ///
    /// Returns an error if called when not in Waiting state or if
    /// the transition is otherwise invalid.
    pub fn start_ascending(&mut self) -> anyhow::Result<()> {
        let mut state = self.state.lock().unwrap();
        match *state {
            CanState::Waiting => {
                *state = CanState::Ascending;
                Ok(())
            }
            CanState::Ascending => Err(anyhow::anyhow!("Ascending already started")),
            _ => Err(anyhow::anyhow!("Invalid state transition")),
        }
    }

    /// Transitions from Ascending to Descending state.
    ///
    /// # Errors
    ///
    /// Returns an error if called when not in Ascending state or if
    /// the transition is otherwise invalid.
    pub fn start_descending(&mut self) -> anyhow::Result<()> {
        let mut state = self.state.lock().unwrap();
        match *state {
            CanState::Ascending => {
                *state = CanState::Descending;
                Ok(())
            }
            CanState::Descending => Err(anyhow::anyhow!("Descending already started")),
            _ => Err(anyhow::anyhow!("Invalid state transition")),
        }
    }

    /// Transitions from Ascending to Landed state.
    ///
    /// # Errors
    ///
    /// Returns an error if called when not in Descending state or if
    /// the transition is otherwise invalid.
    pub fn start_landed(&mut self) -> anyhow::Result<()> {
        let mut state = self.state.lock().unwrap();
        match *state {
            CanState::Descending => {
                *state = CanState::Landed;
                Ok(())
            }
            CanState::Landed => Err(anyhow::anyhow!("Landed already started")),
            _ => Err(anyhow::anyhow!("Invalid state transition")),
        }
    }

    /// Transitions from Landed to Stopping state.
    ///
    /// # Errors
    ///
    /// Returns an error if called when not in Landed state or if
    /// the transition is otherwise invalid.
    pub fn start_stopping(&mut self) -> anyhow::Result<()> {
        let mut state = self.state.lock().unwrap();
        match *state {
            CanState::Landed => {
                *state = CanState::Stopping;
                Ok(())
            }
            CanState::Stopping => Err(anyhow::anyhow!("Stopping already started")),
            _ => Err(anyhow::anyhow!("Invalid state transition")),
        }
    }

    /// Returns the current state of the CanSat.
    ///
    /// This method acquires a lock on the state mutex and returns a copy
    /// of the current state.
    pub fn get_state(&self) -> CanState {
        *self.state.lock().unwrap()
    }
}

/// Macro for changing the state of the CanSat.
///
/// This macro handles the state transition, logging, and sequence incrementing.
///
/// # Arguments
///
/// * `globals` - The global variables containing the state machine and other resources.
/// * `state` - The state to transition to in format `start_`.
#[macro_export]
macro_rules! change_state {
    ($globals:expr, $state:ident) => {
        let previous_state = $globals.state_machine.get_state();
        match $globals.state_machine.$state() {
            Ok(_) => {
                match $globals.xbee_sender.send(
                    &methlink_xbee::header::create_mav_header(
                        methlink_xbee::systems::XbeeSystems::ComputeModule(
                            methlink_xbee::components::ComputeModuleComponents::StateMachine,
                        ),
                    ),
                    &methlink_xbee::methlink::MavMessage::CAN_STATE_CHANGE(
                        methlink_xbee::methlink::CAN_STATE_CHANGE_DATA {
                            from_state: aeros::xbee::converters::state_from_pseudo(&previous_state),
                            to_state: aeros::xbee::converters::state_from_pseudo(
                                &$globals.state_machine.get_state(),
                            ),
                        },
                    ),
                ) {
                    Ok(_) => log::info!("State change message sent"),
                    Err(e) => log::error!("Failed to send state change message: {}", e),
                }
                log::info!("{} started", stringify!($state));
            }
            Err(e) => {
                log::error!("Failed to start {}: {}", stringify!($state), e);
                match $globals.xbee_sender.send(
                    &methlink_xbee::header::create_mav_header(
                        methlink_xbee::systems::XbeeSystems::ComputeModule(
                            methlink_xbee::components::ComputeModuleComponents::StateMachine,
                        ),
                    ),
                    &methlink_xbee::methlink::MavMessage::ERROR(
                        methlink_xbee::methlink::ERROR_DATA {
                            error_code:
                                methlink_xbee::methlink::Error::ERROR_INVALID_STATE_TRANSITION,
                        },
                    ),
                ) {
                    Ok(_) => log::info!("Error message sent"),
                    Err(e) => log::error!("Failed to send error message: {}", e),
                }
            }
        }
    };
    ($globals:expr, $state:ident) => {
        match $globals.state_machine.$state() {
            Ok(_) => log::info!("{} started", stringify!($state)),
            Err(e) => log::error!("Failed to start {}: {}", stringify!($state), e),
        }
    };
}
