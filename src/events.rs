//! This module is to handle events for the StateMachine.
//! Events trigger state transitions in the StateMachine.
//!
//! Events are defined in the `Event` enum.

use crate::send_event;
use std::io::{self, BufRead};

/// Represents the different events that can trigger state transitions.
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum Event {
    UserInput,
    UserAccepted,
    UserDenied,
    AccelerationUp,
    HeightAbove10m,
    AccelerationDownAndStraight,
    HeightBelow10m,
    NoAcceleration,
    StopCommand,
    ConnectionAcknowledged,
}

impl Event {
    /// Returns a string representation of the event.
    pub fn as_str(&self) -> &str {
        match self {
            Event::UserInput => "UserInput",
            Event::UserAccepted => "UserAccepted",
            Event::UserDenied => "UserDenied",
            Event::AccelerationUp => "AccelerationUp",
            Event::HeightAbove10m => "HeightAbove10m",
            Event::AccelerationDownAndStraight => "AccelerationDownAndStraight",
            Event::HeightBelow10m => "HeightBelow10m",
            Event::NoAcceleration => "NoAcceleration",
            Event::StopCommand => "StopCommand",
            Event::ConnectionAcknowledged => "ConnectionAcknowledged",
        }
    }
}

pub fn spawn_keyboard_event_listener(
    tx: std::sync::mpsc::Sender<Event>,
    globals: crate::globals::Globals,
) {
    std::thread::spawn(move || {
        let stdin = io::stdin();
        for line in stdin.lock().lines() {
            match line {
                Ok(_) => {
                    send_event!(globals, Event::UserInput, tx);
                }
                Err(e) => {
                    eprintln!("Error reading line from stdin: {e}");
                    break;
                }
            }
        }
    });
}

#[macro_export]
macro_rules! send_event {
    ($globals:expr, $event:expr, $sender:expr) => {{
        let event = $event;
        if let Err(e) = $sender.send(event) {
            log::error!("Failed to send event {:?}: {}", event, e);
        } else {
            log::info!("Sent event: {:?}", event);
            $globals
                .xbee_sender
                .send(
                    &methlink_xbee::header::create_mav_header(
                        methlink_xbee::systems::XbeeSystems::ComputeModule(
                            methlink_xbee::components::ComputeModuleComponents::StateMachine,
                        ),
                    ),
                    &methlink_xbee::methlink::MavMessage::ERROR(
                        methlink_xbee::methlink::ERROR_DATA {
                            error_code:
                                methlink_xbee::methlink::Error::ERROR_INVALID_STATE_TRANSITION,
                        },
                    ),
                )
                .unwrap_or_else(|e| {
                    log::error!("Failed to send event message: {}", e);
                    0
                });
        }
    }};
}
