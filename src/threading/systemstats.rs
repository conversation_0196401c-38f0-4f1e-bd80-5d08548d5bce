use crate::globals::Globals;
use crate::sensors::systemstats::SystemStats;
use crate::states::CanState;

use methlink_xbee::{components, methlink, systems};

use std::cmp::Ordering;
use std::thread;

use log::warn;

/// System stats thread that writes data to a CSV file.
pub fn system_stats_thread(globals: Globals, termination_state: CanState) {
    let file = format!("{}/system_stats.csv", globals.mission_path);
    let mut writer = csv_writer::CSVWriter::new(
        file,
        vec![
            "index".to_string(),
            "datetime".to_string(),
            "cpu_percent".to_string(),
            "memory_percent".to_string(),
            "cpu_temp".to_string(),
            "cpu_clock".to_string(),
        ],
        SystemStats::new(),
    );

    writer.write_header();

    thread::sleep(std::time::Duration::from_millis(500));

    while globals.state_machine.get_state() != termination_state {
        writer.refresh_data();

        if let Some(Ordering::Greater) = writer
            .data
            .cpu_percentage
            .partial_cmp(&globals.config.system_stats.cpu_usage_threshold)
        {
            globals
                .xbee_sender
                .send(
                    &methlink_xbee::header::create_mav_header(systems::XbeeSystems::ComputeModule(
                        components::ComputeModuleComponents::SystemStats,
                    )),
                    &methlink::MavMessage::WARNING(methlink::WARNING_DATA {
                        warning_code: methlink::Warning::WARNING_CPU_USAGE_HIGH,
                    }),
                )
                .unwrap();
            warn!("CPU usage is too high");
        }

        if let Some(Ordering::Greater) = writer
            .data
            .cpu_temperature
            .partial_cmp(&globals.config.system_stats.cpu_temperature_threshold)
        {
            globals
                .xbee_sender
                .send(
                    &methlink_xbee::header::create_mav_header(systems::XbeeSystems::ComputeModule(
                        components::ComputeModuleComponents::SystemStats,
                    )),
                    &methlink::MavMessage::WARNING(methlink::WARNING_DATA {
                        warning_code: methlink::Warning::WARNING_CPU_TEMPERATURE_HIGH,
                    }),
                )
                .unwrap();
            warn!("CPU temperature is too high");
        }

        if let Some(Ordering::Greater) = writer
            .data
            .memory_percentage
            .partial_cmp(&globals.config.system_stats.memory_usage_threshold)
        {
            globals
                .xbee_sender
                .send(
                    &methlink_xbee::header::create_mav_header(systems::XbeeSystems::ComputeModule(
                        components::ComputeModuleComponents::SystemStats,
                    )),
                    &methlink::MavMessage::WARNING(methlink::WARNING_DATA {
                        warning_code: methlink::Warning::WARNING_MEMORY_USAGE_HIGH,
                    }),
                )
                .unwrap();
            warn!("Memory usage is too high");
        }

        writer.write_data();
        thread::sleep(std::time::Duration::from_millis(500));
    }
}
