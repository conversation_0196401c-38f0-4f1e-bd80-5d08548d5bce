use crate::globals::Globals;
use crate::states::CanState;

use methlink_xbee::{components, methlink, systems};

use std::thread;

use log::{error, info};

pub fn gps_thread(globals: Globals, termination_state: CanState) {
    let mut gps = match serialport::new(&globals.config.gps.port, globals.config.gps.baud_rate)
        .open()
    {
        Ok(port) => {
            info!("GPS port opened");
            port
        }
        Err(e) => {
            error!("Failed to open GPS port: {e}");
            globals
                .xbee_sender
                .send(
                    &methlink_xbee::header::create_mav_header(systems::XbeeSystems::ComputeModule(
                        components::ComputeModuleComponents::GPS,
                    )),
                    &methlink::MavMessage::ERROR(methlink::ERROR_DATA {
                        error_code: methlink::Error::ERROR_GPS_INITIALIZATION_FAILED,
                    }),
                )
                .unwrap();
            return;
        }
    };

    while globals.state_machine.get_state() != termination_state {
        let mut nmea = nmea::Nmea::default();

        let mut buffer = [0; 1024];
        match gps.read(&mut buffer) {
            Ok(size) => {
                info!("Read {size} bytes from GPS");
                match nmea.parse(std::str::from_utf8(&buffer[..size]).unwrap_or("")) {
                    Ok(nmea_message) => {
                        info!("Parsed NMEA message: {nmea_message:?}");
                    }
                    Err(e) => {
                        error!("Failed to parse NMEA message: {e}");
                    }
                }
                println!("NMEA: {nmea:?}");
            }
            Err(e) => {
                error!("Failed to read from GPS: {e}");
            }
        }

        thread::sleep(std::time::Duration::from_secs(1));
    }
}
