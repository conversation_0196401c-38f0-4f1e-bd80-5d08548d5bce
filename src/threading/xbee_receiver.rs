use crate::events::Event;
use crate::globals::Globals;
use crate::send_event;
use crate::states::CanState;
use crate::xbee::converters;

use methlink_xbee::methlink;

use std::{thread, time::Duration};

use log::{error, info, warn};

/// XBee receiver thread that receives MAVLink messages from the XBee module.
pub fn xbee_receiver_thread(
    globals: Globals,
    termination_state: CanState,
    event_sender: std::sync::mpsc::Sender<Event>,
) {
    info!("XBee receiver thread started");

    while globals.state_machine.get_state() != termination_state {
        match globals.xbee_sender.recv() {
            Ok((header, msg)) => {
                info!("Received MAVLink message: {msg:?}");
                info!(
                    "Message header: system_id={}, component_id={}, sequence={}",
                    header.system_id, header.component_id, header.sequence
                );

                match msg {
                    methlink::MavMessage::USER_INPUT(state) => {
                        info!("Received user input event");
                        if globals.state_machine.get_state()
                            == converters::state_to_pseudo(&state.state)
                        {
                            send_event!(globals, Event::UserInput, event_sender);
                        } else {
                            warn!("User input state does not match current state");
                        }
                    }
                    methlink::MavMessage::INITIALIZATION_APPROVAL(approval) => {
                        info!("Received initialization approval request");
                        match approval.approved {
                            0 => send_event!(globals, Event::UserDenied, event_sender),
                            1 => send_event!(globals, Event::UserAccepted, event_sender),
                            _ => warn!(
                                "Invalid initialization approval value: {}",
                                approval.approved
                            ),
                        }
                    }
                    methlink::MavMessage::STOP_COMMAND(_) => {
                        info!("Received stop command");
                        send_event!(globals, Event::StopCommand, event_sender);
                    }
                    methlink::MavMessage::CONNECTION_ACKNOWLEDGED(_) => {
                        info!("Received connection acknowledged message");
                        send_event!(globals, Event::ConnectionAcknowledged, event_sender);
                    }
                    _ => {
                        warn!("Unhandled MAVLink message: {msg:?}");
                    }
                }
            }
            Err(mavlink::error::MessageReadError::Io(io_err)) => {
                if io_err.kind() == std::io::ErrorKind::WouldBlock {
                    thread::sleep(Duration::from_millis(100));
                    continue;
                } else if io_err.kind() == std::io::ErrorKind::UnexpectedEof {
                    info!("XBee connection closed");
                    break;
                } else {
                    error!("Error reading MAVLink message: {io_err}");
                }
            }
            Err(e) => {
                error!("Error reading MAVLink message: {e}");
            }
        }
    }

    info!("XBee receiver thread stopped");
}
