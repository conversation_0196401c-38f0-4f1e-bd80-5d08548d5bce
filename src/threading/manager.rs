use super::{bme280, color_camera, gps, systemstats, thermal_camera, xbee_receiver};
use crate::events::Event;
use crate::globals::Globals;
use crate::states::CanState;

use std::collections::HashMap;

#[derive(<PERSON>ialEq, Eq, <PERSON><PERSON>, <PERSON><PERSON>, Hash, Debug)]
pub enum ThreadType {
    Bme280,
    ColorCamera,
    Gps,
    SystemStats,
    ThermalCamera,
    XbeeReceiver,
}

impl Iterator for ThreadType {
    type Item = ThreadType;

    fn next(&mut self) -> Option<Self::Item> {
        match self {
            ThreadType::Bme280 => {
                *self = ThreadType::ColorCamera;
                Some(ThreadType::Bme280)
            }
            ThreadType::ColorCamera => {
                *self = ThreadType::Gps;
                Some(ThreadType::ColorCamera)
            }
            ThreadType::Gps => {
                *self = ThreadType::SystemStats;
                Some(ThreadType::Gps)
            }
            ThreadType::SystemStats => {
                *self = ThreadType::ThermalCamera;
                Some(ThreadType::SystemStats)
            }
            ThreadType::ThermalCamera => {
                *self = ThreadType::XbeeReceiver;
                Some(ThreadType::ThermalCamera)
            }
            ThreadType::XbeeReceiver => None,
        }
    }
}

pub struct Manager {
    threads: HashMap<ThreadType, std::thread::JoinHandle<()>>,
    globals: Globals,
}

impl Manager {
    pub fn new(globals: Globals) -> Manager {
        Manager {
            threads: HashMap::new(),
            globals,
        }
    }

    pub fn stop_all(&mut self) {
        for (_, thread) in self.threads.drain() {
            thread.join().unwrap();
        }
    }

    pub fn start_thread(
        &mut self,
        thread_type: ThreadType,
        termination_state: CanState,
        event_sender: std::sync::mpsc::Sender<Event>,
    ) {
        match thread_type {
            ThreadType::Bme280 => {
                let globals = self.globals.clone();
                self.threads.insert(
                    ThreadType::Bme280,
                    std::thread::spawn(move || bme280::bme280_thread(globals, termination_state)),
                );
            }
            ThreadType::ColorCamera => {
                let globals = self.globals.clone();
                self.threads.insert(
                    ThreadType::ColorCamera,
                    std::thread::spawn(move || {
                        color_camera::color_camera_thread(globals, termination_state)
                    }),
                );
            }
            ThreadType::Gps => {
                let globals = self.globals.clone();
                self.threads.insert(
                    ThreadType::Gps,
                    std::thread::spawn(move || gps::gps_thread(globals, termination_state)),
                );
            }
            ThreadType::SystemStats => {
                let globals = self.globals.clone();
                self.threads.insert(
                    ThreadType::SystemStats,
                    std::thread::spawn(move || {
                        systemstats::system_stats_thread(globals, termination_state)
                    }),
                );
            }
            ThreadType::ThermalCamera => {
                let globals = self.globals.clone();
                self.threads.insert(
                    ThreadType::ThermalCamera,
                    std::thread::spawn(move || {
                        thermal_camera::thermal_camera_thread(globals, termination_state)
                    }),
                );
            }
            ThreadType::XbeeReceiver => {
                let globals = self.globals.clone();
                self.threads.insert(
                    ThreadType::XbeeReceiver,
                    std::thread::spawn(move || {
                        xbee_receiver::xbee_receiver_thread(
                            globals,
                            termination_state,
                            event_sender,
                        )
                    }),
                );
            }
        }
    }

    pub fn stop_thread(&mut self, thread_type: ThreadType) {
        if let Some(thread) = self.threads.remove(&thread_type) {
            thread.join().unwrap();
        }
    }
}
