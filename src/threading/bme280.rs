use crate::globals::Globals;
use crate::sensors::bme280::Bme280;
use crate::states::CanState;

use methlink_xbee::{components, methlink, systems};

use std::thread;

/// BME280 thread that writes data to a CSV file.
pub fn bme280_thread(globals: Globals, termination_state: CanState) {
    let file = format!("{}/bme280.csv", globals.mission_path);
    let mut writer = csv_writer::CSVWriter::new(
        file,
        vec![
            "index".to_string(),
            "datetime".to_string(),
            "temperature".to_string(),
            "pressure".to_string(),
            "humidity".to_string(),
            "altitude".to_string(),
            "relative_altitude".to_string(),
        ],
        match Bme280::new(&globals.config.bme280) {
            Ok(bme280) => bme280,
            Err(e) => {
                globals
                    .xbee_sender
                    .send(
                        &methlink_xbee::header::create_mav_header(
                            systems::XbeeSystems::ComputeModule(
                                components::ComputeModuleComponents::Bme280,
                            ),
                        ),
                        &methlink::MavMessage::ERROR(methlink::ERROR_DATA {
                            error_code: methlink::Error::ERROR_BME280_INITIALIZATION_FAILED,
                        }),
                    )
                    .unwrap();
                log::error!("Failed to initialize BME280: {e}");
                return;
            }
        },
    );

    writer.write_header();

    thread::sleep(std::time::Duration::from_millis(
        1000 / globals.config.bme280.fps,
    ));

    while globals.state_machine.get_state() != termination_state {
        writer.refresh_data();

        globals
            .xbee_sender
            .send(
                &methlink_xbee::header::create_mav_header(systems::XbeeSystems::ComputeModule(
                    components::ComputeModuleComponents::Bme280,
                )),
                &methlink::MavMessage::BME280(methlink::BME280_DATA {
                    temperature: writer.data.temperature,
                    pressure: writer.data.pressure,
                    humidity: writer.data.humidity,
                    altitude: writer.data.altitude,
                }),
            )
            .unwrap();

        writer.write_data();
        thread::sleep(std::time::Duration::from_millis(
            1000 / globals.config.bme280.fps,
        ));
    }
}
