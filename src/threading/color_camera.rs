use crate::globals::Globals;
use crate::states::CanState;

use chrono::Local;
use std::thread;
use std::time::Duration;

use log::{error, info};

use gstreamer::prelude::*;

pub fn color_camera_thread(globals: Globals, termination_state: CanState) {
    match gstreamer::init() {
        Ok(_) => info!("GStreamer initialized"),
        Err(e) => {
            error!("Failed to initialize GStreamer: {e}");
            return;
        }
    }

    let pipeline = match gstreamer::parse::launch(&format!(
        "v4l2src device={} \
        ! video/x-raw,width={},height={},framerate={}/1 \
        ! videoconvert \
        ! x264enc tune=zerolatency bitrate=2000 speed-preset=ultrafast key-int-max=30 byte-stream=true \
        ! h264parse config-interval=-1 \
        ! mp4mux faststart=true \
        ! filesink location={}/color_camera_{}.mp4",
        globals.config.color_camera.path,
        globals.config.color_camera.width,
        globals.config.color_camera.height,
        globals.config.color_camera.fps,
        globals.mission_path,
        Local::now().format("%Y-%m-%d_%H-%M-%S")
    )) {
        Ok(pipeline) => pipeline,
        Err(e) => {
            error!("Failed to create GStreamer pipeline: {e}");
            return;
        }
    };

    match pipeline.set_state(gstreamer::State::Playing) {
        Ok(_) => info!("GStreamer pipeline started"),
        Err(e) => {
            error!("Failed to start GStreamer pipeline: {e}");
            return;
        }
    }

    while globals.state_machine.get_state() != termination_state {
        thread::sleep(Duration::from_millis(100));
    }

    pipeline.send_event(gstreamer::event::Eos::new());
    let bus = match pipeline.bus() {
        Some(bus) => bus,
        None => {
            error!("Failed to get GStreamer pipeline bus");
            return;
        }
    };
    for msg in bus.iter_timed(gstreamer::ClockTime::NONE) {
        match msg.view() {
            gstreamer::MessageView::Eos(..) | gstreamer::MessageView::Error(_) => break,
            _ => (),
        }
    }

    match pipeline.set_state(gstreamer::State::Null) {
        Ok(_) => info!("GStreamer pipeline stopped"),
        Err(e) => {
            error!("Failed to stop GStreamer pipeline: {e}");
        }
    }
}
