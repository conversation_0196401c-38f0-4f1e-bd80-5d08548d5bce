use crate::globals::Globals;
#[cfg(feature = "legacy_thermal_camera")]
use crate::sensors::thermal_camera::ThermalCamera;
use crate::states::CanState;
#[cfg(feature = "legacy_thermal_camera")]
use methlink_xbee::header;

#[cfg(feature = "legacy_thermal_camera")]
use methlink_xbee::{components, methlink, systems};

use std::thread;

use gstreamer::prelude::*;

#[cfg(feature = "legacy_thermal_camera")]
/// Thermal camera thread that writes data to a CSV file.
pub fn thermal_camera_thread(globals: Globals, termination_state: CanState) {
    let file = format!("{}/thermal_camera.csv", globals.mission_path);
    let mut writer = csv_writer::CSVWriter::new(
        file,
        vec![
            "index".to_string(),
            "datetime".to_string(),
            "thermal_image".to_string(),
        ],
        match ThermalCamera::new(&globals.config.thermal_camera_legacy) {
            Ok(camera) => camera,
            Err(e) => {
                globals
                    .xbee_sender
                    .send(
                        &header::create_mav_header(systems::XbeeSystems::ComputeModule(
                            components::ComputeModuleComponents::ThermalCamera,
                        )),
                        &methlink::MavMessage::ERROR(methlink::ERROR_DATA {
                            error_code: methlink::Error::ERROR_THERMAL_CAMERA_INITIALIZATION_FAILED,
                        }),
                    )
                    .unwrap();
                log::error!("Failed to initialize thermal camera: {e}");
                return;
            }
        },
    );

    writer.write_header();

    thread::sleep(std::time::Duration::from_millis(16));

    while globals.state_machine.get_state() != termination_state {
        writer.refresh_data();
        writer.write_data();
        thread::sleep(std::time::Duration::from_millis(
            ((1000 / globals.config.thermal_camera_legacy.fps) - 15) as u64,
        ));
    }
}

#[cfg(not(feature = "legacy_thermal_camera"))]
pub fn thermal_camera_thread(globals: Globals, termination_state: CanState) {
    match gstreamer::init() {
        Ok(_) => log::info!("GStreamer initialized"),
        Err(e) => {
            log::error!("Failed to initialize GStreamer: {e}");
            return;
        }
    }

    let pipeline = match gstreamer::parse::launch(&format!(
        "v4l2src device={} \
        ! video/x-raw,format=YUY2,width={},height={},framerate={}/1 \
        ! videoconvert \
        ! x264enc \
        ! mp4mux \
        ! filesink location={}/thermal_{}.mp4",
        globals.config.thermal_camera.path,
        globals.config.thermal_camera.width,
        globals.config.thermal_camera.height,
        globals.config.thermal_camera.fps,
        globals.mission_path,
        chrono::Local::now().format("%Y-%m-%d_%H-%M-%S")
    )) {
        Ok(pipeline) => pipeline,
        Err(e) => {
            log::error!("Failed to create GStreamer pipeline: {e}");
            return;
        }
    };

    match pipeline.set_state(gstreamer::State::Playing) {
        Ok(_) => log::info!("GStreamer pipeline started"),
        Err(e) => {
            log::error!("Failed to start GStreamer pipeline: {e}");
            return;
        }
    }
    while globals.state_machine.get_state() != termination_state {
        thread::sleep(std::time::Duration::from_millis(100));
    }
    pipeline.send_event(gstreamer::event::Eos::new());
    let bus = match pipeline.bus() {
        Some(bus) => bus,
        None => {
            log::error!("Failed to get GStreamer pipeline bus");
            return;
        }
    };
    for msg in bus.iter_timed(gstreamer::ClockTime::NONE) {
        match msg.view() {
            gstreamer::MessageView::Eos(..) | gstreamer::MessageView::Error(_) => break,
            _ => (),
        }
    }

    match pipeline.set_state(gstreamer::State::Null) {
        Ok(_) => log::info!("GStreamer pipeline stopped"),
        Err(e) => log::error!("Failed to stop GStreamer pipeline: {e}"),
    }
}
