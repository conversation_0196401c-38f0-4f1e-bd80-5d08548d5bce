//! Contains functions to convert internally used types to the types used by methlink.

use crate::states::CanState;
use methlink_xbee::methlink;

/// Converts the CanSat state to the methlink state.
pub fn state_from_pseudo(canstate: &CanState) -> methlink::CanState {
    match canstate {
        CanState::Initializing => methlink::CanState::MAV_STATE_INITIALIZING,
        CanState::Testing => methlink::CanState::MAV_STATE_TESTING,
        CanState::Waiting => methlink::CanState::MAV_STATE_WAITING,
        CanState::Ascending => methlink::CanState::MAV_STATE_ASCENDING,
        CanState::Descending => methlink::CanState::MAV_STATE_DESCENDING,
        CanState::Landed => methlink::CanState::MAV_STATE_LANDED,
        CanState::Stopping => methlink::CanState::MAV_STATE_STOPPING,
    }
}

/// Converts the CanSat state to the methlink state.
pub fn state_to_pseudo(mavstate: &methlink::CanState) -> CanState {
    match mavstate {
        methlink::CanState::MAV_STATE_INITIALIZING => CanState::Initializing,
        methlink::CanState::MAV_STATE_TESTING => CanState::Testing,
        methlink::CanState::MAV_STATE_WAITING => CanState::Waiting,
        methlink::CanState::MAV_STATE_ASCENDING => CanState::Ascending,
        methlink::CanState::MAV_STATE_DESCENDING => CanState::Descending,
        methlink::CanState::MAV_STATE_LANDED => CanState::Landed,
        methlink::CanState::MAV_STATE_STOPPING => CanState::Stopping,
    }
}
