#[macro_export]
macro_rules! send_heartbeat {
    ($globals:expr) => {
        if let Err(e) = $globals.xbee_sender.send(
            &methlink_xbee::header::create_mav_header(
                methlink_xbee::systems::XbeeSystems::ComputeModule(
                    methlink_xbee::components::ComputeModuleComponents::Main,
                ),
            ),
            &methlink_xbee::methlink::MavMessage::HEARTBEAT(
                methlink_xbee::methlink::HEARTBEAT_DATA {
                    state: aeros::xbee::converters::state_from_pseudo(
                        &$globals.state_machine.get_state(),
                    ),
                    mission_time: $globals
                        .mission_start_time
                        .lock()
                        .map(|start_time| (chrono::Local::now() - *start_time).num_seconds() as u64)
                        .unwrap_or(0),
                },
            ),
        ) {
            log::warn!("Failed to send heartbeat: {}", e);
        }
        log::info!("Heartbeat sent");
    };
}
