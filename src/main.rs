use aeros::config::Config;
use aeros::events::*;
use aeros::globals::Globals;
use aeros::states::{CanState, StateMachine};
use aeros::threading::manager::Manager;
use aeros::threading::manager::ThreadType;
use aeros::{buzzer, change_state, send_heartbeat};
use methlink_xbee::{components, methlink, systems};

use chrono::Local;

use std::fs::DirBuilder;
use std::fs::{self, File};
use std::path::Path;
use std::sync::Arc;

use log::LevelFilter;
#[allow(unused_imports)]
use log::{error, info, warn};
use simplelog::{ColorChoice, CombinedLogger, TermLogger, TerminalMode, WriteLogger};

use std::sync::mpsc;
use std::thread;
use std::time::Duration;

fn main() {
    let config_file = fs::read_to_string("config.toml").expect("Failed to read config file");
    let config: Config = toml::from_str(&config_file).expect("Failed to parse config file");

    let mission_path = format!(
        "{}-{}",
        &config.mission.path,
        Local::now().format("%Y-%m-%d_%H-%M-%S")
    );

    let base_dir = Path::new(&config.mission.path)
        .parent()
        .unwrap_or(Path::new("."));
    if !base_dir.exists() {
        DirBuilder::new()
            .recursive(true)
            .create(base_dir)
            .expect("Couldn't create base data directory!");
    }

    if !Path::new(&mission_path).exists() {
        DirBuilder::new()
            .recursive(true)
            .create(&mission_path)
            .expect("Couldn't create mission directory!");
    }

    let log_level = config
        .logging
        .level
        .parse::<LevelFilter>()
        .unwrap_or(LevelFilter::Info);
    let log_path = &config.logging.path;

    let log_file = format!(
        "{}/aeros-{}.log",
        log_path,
        Local::now().format("%Y-%m-%d_%H-%M-%S")
    );

    if !Path::new(log_path).exists() {
        DirBuilder::new()
            .recursive(true)
            .create(log_path)
            .expect("Couldn't create logs directory!");
    }

    CombinedLogger::init(vec![
        TermLogger::new(
            LevelFilter::Warn,
            simplelog::Config::default(),
            TerminalMode::Mixed,
            ColorChoice::Auto,
        ),
        WriteLogger::new(
            log_level,
            simplelog::Config::default(),
            File::create(log_file).expect("Couldn't create logfile!"),
        ),
    ])
    .expect("Couldn't setup combined logger!");

    info!("Statemachine initialized");
    let mission_state = StateMachine::default();
    let (event_tx, event_rx) = mpsc::channel();

    let mavlink_arg = format!("serial:{}:{}", &config.xbee.port, &config.xbee.baud_rate);

    let mut xbee_sender: Box<dyn mavlink::MavConnection<methlink::MavMessage> + Send + Sync> =
        match mavlink::connect::<methlink::MavMessage>(&mavlink_arg) {
            Ok(sender) => sender,
            Err(e) => {
                let file = format!("{mission_path}/xbee_fallback.txt");
                // Create file with write permissions
                File::create(&file).expect("Couldn't create fallback file!");

                error!("Failed to connect to Xbee: {e} Now using fallback file {file}");

                let fallback_arg = format!("file:{file}");
                mavlink::connect::<methlink::MavMessage>(&fallback_arg).unwrap()
            }
        };

    xbee_sender.set_protocol_version(mavlink::MavlinkVersion::V1);

    #[allow(unused_mut)]
    let mut globals = Globals::new(mission_path, mission_state, config, Arc::new(xbee_sender));

    let mut manager = Manager::new(globals.clone());

    manager.start_thread(
        ThreadType::XbeeReceiver,
        CanState::Stopping,
        event_tx.clone(),
    );

    #[cfg(debug_assertions)]
    spawn_keyboard_event_listener(event_tx.clone(), globals.clone());

    loop {
        globals
            .xbee_sender
            .send(
                &methlink_xbee::header::create_mav_header(systems::XbeeSystems::ComputeModule(
                    components::ComputeModuleComponents::Main,
                )),
                &methlink::MavMessage::CONNECTION_REQUEST(methlink::CONNECTION_REQUEST_DATA {}),
            )
            .unwrap();

        match event_rx.recv_timeout(Duration::from_secs(1)) {
            Ok(Event::ConnectionAcknowledged) => break,
            Ok(Event::UserInput) => break,
            Ok(_) => continue,
            Err(mpsc::RecvTimeoutError::Timeout) => continue,
            Err(mpsc::RecvTimeoutError::Disconnected) => continue,
        }
    }

    let buzzer = buzzer::initialize_buzzer(&globals).unwrap_or_else(|err| {
        error!("Failed to initialize buzzer: {err}");
        None
    });

    change_state!(globals.clone(), start_testing);

    globals
        .xbee_sender
        .send(
            &methlink_xbee::header::create_mav_header(systems::XbeeSystems::ComputeModule(
                components::ComputeModuleComponents::Main,
            )),
            &methlink::MavMessage::WARNING(methlink::WARNING_DATA {
                warning_code: methlink::Warning::WARNING_TEST,
            }),
        )
        .unwrap();

    globals
        .xbee_sender
        .send(
            &methlink_xbee::header::create_mav_header(systems::XbeeSystems::ComputeModule(
                components::ComputeModuleComponents::Main,
            )),
            &methlink::MavMessage::ERROR(methlink::ERROR_DATA {
                error_code: methlink::Error::ERROR_TEST,
            }),
        )
        .unwrap();

    change_state!(globals.clone(), start_waiting);

    *globals.mission_start_time.lock().unwrap() = chrono::Local::now();

    manager.start_thread(
        ThreadType::ThermalCamera,
        CanState::Stopping,
        event_tx.clone(),
    );

    manager.start_thread(
        ThreadType::ColorCamera,
        CanState::Stopping,
        event_tx.clone(),
    );

    manager.start_thread(ThreadType::Gps, CanState::Stopping, event_tx.clone());

    manager.start_thread(
        ThreadType::SystemStats,
        CanState::Stopping,
        event_tx.clone(),
    );

    manager.start_thread(ThreadType::Bme280, CanState::Stopping, event_tx.clone());

    println!("Enter to start ascent");

    loop {
        match event_rx.try_recv() {
            Ok(Event::UserInput) => break,
            Ok(Event::HeightAbove10m) => break,
            Ok(Event::AccelerationUp) => break,
            Ok(_) => {
                send_heartbeat!(globals);
                thread::sleep(Duration::from_secs(1));
            }
            Err(mpsc::TryRecvError::Empty) => {
                send_heartbeat!(globals);
                thread::sleep(Duration::from_secs(1));
            }
            Err(mpsc::TryRecvError::Disconnected) => break,
        }
    }

    change_state!(globals.clone(), start_ascending);

    println!("Enter to start descent");

    loop {
        match event_rx.try_recv() {
            Ok(Event::UserInput) => break,
            Ok(Event::AccelerationDownAndStraight) => break,
            Ok(_) => {
                send_heartbeat!(globals);
                thread::sleep(Duration::from_secs(1));
            }
            Err(mpsc::TryRecvError::Empty) => {
                send_heartbeat!(globals);
                thread::sleep(Duration::from_secs(1));
            }
            Err(mpsc::TryRecvError::Disconnected) => break,
        }
    }

    change_state!(globals.clone(), start_descending);

    //start flywheel

    println!("Enter to start landing");

    loop {
        match event_rx.try_recv() {
            Ok(Event::UserInput) => break,
            Ok(Event::HeightBelow10m) => break,
            Ok(Event::NoAcceleration) => break,
            Ok(_) => {
                send_heartbeat!(globals);
                thread::sleep(Duration::from_secs(1));
            }
            Err(mpsc::TryRecvError::Empty) => {
                send_heartbeat!(globals);
                thread::sleep(Duration::from_secs(1));
            }
            Err(mpsc::TryRecvError::Disconnected) => break,
        }
    }

    change_state!(globals.clone(), start_landed);

    buzzer::start_bussin(&buzzer, &globals).unwrap_or_else(|err| {
        error!("Failed to start buzzer: {err}");
    });

    println!("Enter to start stopping");
    loop {
        match event_rx.try_recv() {
            Ok(Event::UserInput) => break,
            Ok(Event::StopCommand) => break,
            Ok(_) => {
                send_heartbeat!(globals);
                thread::sleep(Duration::from_secs(1));
            }
            Err(mpsc::TryRecvError::Empty) => {
                send_heartbeat!(globals);
                thread::sleep(Duration::from_secs(1));
            }
            Err(mpsc::TryRecvError::Disconnected) => break,
        }
    }

    change_state!(globals.clone(), start_stopping);

    buzzer::stop_bussin(&buzzer, &globals).unwrap_or_else(|err| {
        error!("Failed to stop buzzer: {err}");
    });

    // Currently, threads do not terminate on state change to Stopping.
    manager.stop_all();
}
