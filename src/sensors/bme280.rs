//! This module is to interface with the BME280 sensor hardware.

use csv_writer::CSVData;

use anyhow::Result;
use chrono::Local;

use bme280::i2c::BME280;
use linux_embedded_hal::{Delay, I2cdev};

/// Struct to hold BME280 data.
pub struct Bme280 {
    bme280: BME280<I2cdev>,
    index: i32,
    timestamp: chrono::DateTime<Local>,
    pub temperature: f32,
    pub pressure: f32,
    pub humidity: f32,
    pub altitude: f32,
    pub relative_altitude: f32,
    pub base_altitude: f32,
}

impl Bme280 {
    pub fn new(config: &crate::config::Bme280) -> Result<Self, anyhow::Error> {
        let i2c = I2cdev::new(config.i2c_bus.as_str())?;
        let mut bme280 = BME280::new(i2c, config.address);
        bme280
            .init(&mut Delay)
            .map_err(|_| anyhow::anyhow!("Failed to initialize BME280"))?;
        let initial_measurement = bme280
            .measure(&mut Delay)
            .map_err(|e| anyhow::anyhow!("Failed to measure BME280: {:?}", e))?;
        Ok(Bme280 {
            bme280,
            index: -1,
            timestamp: Local::now(),
            temperature: 0.0,
            pressure: 0.0,
            humidity: 0.0,
            altitude: 0.0,
            relative_altitude: 0.0,
            base_altitude: Self::calculate_altitude(initial_measurement.pressure),
        })
    }

    fn get_index(&self) -> i32 {
        self.index + 1
    }

    pub fn calculate_altitude(pressure: f32) -> f32 {
        // Formula for altitude calculation
        // h = 44330 * (1 - (p / p0) ^ (1 / 5.256))
        // p0 = 101325 Pa (standard sea level pressure)
        // p = pressure in Pa

        44330.0 * (1.0 - (pressure / 101325.0).powf(1.0 / 5.256))
    }

    /// Calculate relative altitude based on base altitude
    pub fn calculate_relative_altitude(&mut self, base_altitude: f32) {
        self.relative_altitude = self.altitude - base_altitude;
    }
}

impl CSVData for Bme280 {
    fn refresh(&mut self) {
        let measurement = self.bme280.measure(&mut Delay).unwrap();
        self.index = self.get_index();
        self.timestamp = Local::now();
        self.temperature = measurement.temperature;
        self.pressure = measurement.pressure;
        self.humidity = measurement.humidity;
        self.altitude = Self::calculate_altitude(self.pressure);
        self.calculate_relative_altitude(self.base_altitude);
    }

    fn as_csv_record(&self) -> Vec<String> {
        vec![
            self.index.to_string(),
            self.timestamp.to_string(),
            self.temperature.to_string(),
            self.pressure.to_string(),
            self.humidity.to_string(),
            self.altitude.to_string(),
            self.relative_altitude.to_string(),
        ]
    }
}
