//! This module is to interface with the MLX90640 thermal camera hardware.

use crate::config;
use csv_writer::CSVData;

use chrono::Local;
use log::info;

use linux_embedded_hal::I2cdev;
use mlx9064x::Mlx90640Driver;

/// Represents a thermal camera that captures temperature data.
///
/// This struct interfaces with the MLX90640 thermal camera hardware
/// through the rpmlx90640 crate to read temperature values.
pub struct ThermalCamera {
    camera: Mlx90640Driver<I2cdev>,
    /// Sequential index for data records
    index: i32,
    /// Timestamp when the data was captured
    timestamp: chrono::DateTime<Local>,
    /// Array of 768 temperature readings (32x24 thermal sensor)
    temperatures: Vec<f32>,
}

impl ThermalCamera {
    /// Creates a new ThermalCamera instance with initial values.
    pub fn new(config: &config::ThermalCameraLegacy) -> anyhow::Result<Self> {
        info!("Initializing thermal camera...");
        let mut camera = Mlx90640Driver::new(I2cdev::new(&config.bus)?, config.addr)
            .map_err(|e| anyhow::anyhow!("Could not initialize thermal camera: {}", e))?;
        camera
            .set_frame_rate(mlx9064x::FrameRate::SixtyFour)
            .unwrap();
        Ok(ThermalCamera {
            temperatures: Self::get_temperatures(&mut camera).unwrap(),
            camera,
            index: -1,
            timestamp: Local::now(),
        })
    }

    /// Returns the next index value by incrementing the current index.
    fn get_index(&self) -> i32 {
        self.index + 1
    }

    /// Reads temperature data from the MLX90640 thermal camera.
    ///
    /// Returns an array of 768 temperature values in degrees Celsius.
    /// If an error occurs during reading, returns an array filled with -1000.0.
    fn get_temperatures(camera: &mut Mlx90640Driver<I2cdev>) -> anyhow::Result<Vec<f32>> {
        info!("Reading thermal camera...");
        let mut temperatures = vec![0f32; camera.height() * camera.width()];
        camera
            .generate_image_if_ready(&mut temperatures)
            .map_err(|e| anyhow::anyhow!("Could not read thermal camera: {}", e))?;
        std::thread::sleep(std::time::Duration::from_millis(16));
        camera.generate_image_if_ready(&mut temperatures)?;
        Ok(temperatures)
    }
}

impl CSVData for ThermalCamera {
    /// Updates the ThermalCamera with fresh data.
    fn refresh(&mut self) {
        self.index = self.get_index();
        self.timestamp = Local::now();
        self.temperatures = Self::get_temperatures(&mut self.camera).unwrap();
    }

    /// Converts the thermal camera data to a CSV record format.
    ///
    /// Returns a vector of strings containing:
    /// - index
    /// - timestamp
    /// - temperature array (as debug format)
    fn as_csv_record(&self) -> Vec<String> {
        vec![
            self.index.to_string(),
            self.timestamp.to_string(),
            format!("{:?}", self.temperatures),
        ]
    }
}
