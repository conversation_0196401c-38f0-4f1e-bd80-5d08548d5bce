use crate::config::Config;
use crate::states::StateMachine;

use methlink_xbee::methlink;

use std::sync::{Arc, Mutex};

/// Global variables for the application.
#[derive(Clone)]
pub struct Globals {
    pub mission_path: String,
    pub mission_start_time: Arc<Mutex<chrono::DateTime<chrono::Local>>>,
    pub config: Config,
    pub state_machine: StateMachine,
    pub xbee_sender: Arc<Box<dyn mavlink::MavConnection<methlink::MavMessage> + Send + Sync>>,
    pub base_altitude: Arc<Mutex<Option<f32>>>,
}

impl Globals {
    pub fn new(
        mission_path: String,
        state_machine: StateMachine,
        config: Config,
        xbee_sender: Arc<Box<dyn mavlink::MavConnection<methlink::MavMessage> + Send + Sync>>,
    ) -> Globals {
        Globals {
            mission_path,
            mission_start_time: Arc::new(Mutex::new(chrono::Local::now())),
            config,
            state_machine,
            xbee_sender,
            base_altitude: Arc::new(Mutex::new(None)),
        }
    }
}
