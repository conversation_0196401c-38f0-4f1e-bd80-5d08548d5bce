@startmindmap Systems Mindmap

skinparam backgroundColor #FFFFFF
skinparam shadowing false
skinparam defaultTextAlignment center
skinparam node {
  BackgroundColor #219cd3
  FontColor Black
  BorderColor #3a9bd3
}
skinparam hyperlinkColor Black

+ [[ https://wiki.aerospace-lab.de/spaces/PROJ/pages/178913283/CanSat+25+26 CanSat ]]
++ [[ https://wiki.aerospace-lab.de/spaces/PROJ/pages/196706309/Flywheel+Gyroskop Flywheel ]]
+++ Motor
+++ [[ https://wiki.aerospace-lab.de/spaces/PROJ/pages/203816961/Motor+Controller ESC ]]
+++ [[ https://wiki.aerospace-lab.de/spaces/PROJ/pages/190742547/Accelerometer 9-Achs Gyroskop ]]
+++ Microcontroller
+++ Struktur
++ Struktur
++ Payload
+++ Primärmission
++++ [[ https://wiki.aerospace-lab.de/spaces/PROJ/pages/190742543/Temperature+Humidity+and+Pressure Sensor Luftdaten ]]
+++ Sekundärmission
++++ [[ https://wiki.aerospace-lab.de/spaces/PROJ/pages/261783555/W%C3%A4rmebildkameras Wärmbildkamera ]]
++++ Farbkamera
++ [[ https://wiki.aerospace-lab.de/spaces/PROJ/pages/205226037/Hauptplatine Bordcomputer/OBDH ]]
+++ [[ https://wiki.aerospace-lab.de/spaces/PROJ/pages/238649405/AerOS Compute Module 4 ]]
+++ Datenbus
+++ [[ https://wiki.aerospace-lab.de/spaces/PROJ/pages/214728706/GPS GPS ]]
-- [[ https://wiki.aerospace-lab.de/spaces/PROJ/pages/230588433/An+Aus+Platine Stromversorgung ]]
--- Akku
--- Spannungswandler
--- Lademanagement
-- [[ https://wiki.aerospace-lab.de/spaces/PROJ/pages/196706313/Fallschirm Fallschirm/Recovery System ]]
--- Fallschirm
--- Aufhängung
-- [[ https://wiki.aerospace-lab.de/spaces/PROJ/pages/190742563/Communication Kommunikation ]]
--- [[ https://wiki.aerospace-lab.de/spaces/PROJ/pages/196280332/Xbee XBee ]]
--- Antenne
--- [[ https://wiki.aerospace-lab.de/spaces/PROJ/pages/234192902/Methlink Protokoll ]]
@endmindmap