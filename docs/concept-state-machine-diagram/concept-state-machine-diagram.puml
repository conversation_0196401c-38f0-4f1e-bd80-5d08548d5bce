@startuml Concept AerOS State Machine

skinparam BackgroundColor #FFFFFF

skinparam state {
  BackgroundColor #219cd3
}

skinparam title {
    BackgroundColor #3a9bd3
    FontColor Black
    BorderRoundCorner 15
    FontSize 18
    FontStyle Bold
}

title Concept AerOS State Machine

left to right direction

state Initializing {
    state InitializeXbee
    state WaitForGroundStationConnection
    state InitializeBuzzer
    InitializeXbee -right-> WaitForGroundStationConnection
    WaitForGroundStationConnection -right-> InitializeBuzzer
}

state Testing {
    state SendTestWarning
    state SendTestError
    SendTestWarning -right-> SendTestError
}

state Waiting {
    state WaitUntilStart
    state StartRecording
    state "RunMainLoop" as RunMainLoop_Waiting
    StartRecording: ThermalCamera\nColorCamera\nBme280\nSystemStats
    WaitUntilStart -right-> StartRecording
    StartRecording -right-> RunMainLoop_Waiting
}

state Ascending {
    state "RunMainLoop" as RunMainLoop_Ascending
}

state Descending {
    state StartFlywheel
    state "RunMainLoop" as Run<PERSON><PERSON><PERSON>oop_Descending
    StartFlywheel -right-> RunMainLoop_Descending
}

state Landed {
    state ActivateBuzzer
    state "RunMainLoop" as Run<PERSON>ainLoop_Landed
    ActivateBuzzer -right-> RunMainLoop_Landed
}

state Stopping {
    state StopRecording
    StopRecording: ThermalCamera\nColorCamera\nBme280\nSystemStats
    state StopBuzzer
    StopRecording -right-> StopBuzzer
}

state UserInput <<choice>>

[*] -down-> Initializing
Initializing -down-> Testing : InitializationComplete
Testing -down-> UserInput : TestingComplete
UserInput -left-> Initializing : Rejected by aerosctl
UserInput -down-> Waiting : Accepted by aerosctl
Waiting -right-> Ascending : Relative Height > 10m | Acceleration up 
Ascending -up-> Descending : Acceleration up 0 and CanSat straight
Descending -up-> Landed : Relative Height < 10m | No Acceleration
Landed -up-> Stopping : StopCommand
Stopping -up-> [*]

@enduml