@startuml Current AerOS State Machine

skinparam BackgroundColor #FFFFFF

skinparam state {
  BackgroundColor #219cd3
}

title Current AerOS State Machine

state Initializing {
    state InitializeXbee
    state InitializeBuzzer
    InitializeXbee --> InitializeBuzzer
}

state Testing {
    state SendTestWarning
    state SendTestError
    SendTestWarning --> SendTestError
}

state Waiting {
    state WaitUntilStart
}

state Mission {
    state StartRecording
    StartRecording: ThermalCamera\nColorCamera\nBme280\nSystemStats
    
}

state Landing {
    state ActivateBuzzer
    
}

state Stopping {
    state StopRecording
    StopRecording: ThermalCamera\nColorCamera\nBme280\nSystemStats
    state StopBuzzer
    StopRecording --> StopBuzzer
}

[*] -right-> Initializing
Initializing -right-> Testing : InitializationComplete
Testing -right-> Waiting : TestingComplete
Waiting -down-> Mission : KeyboardInput
Mission -left-> Landing : KeyboardInput
Landing -left-> Stopping : KeyboardInput
Stopping -left-> [*]

@enduml
