@startuml Architecture

title CanSat Project - System Architecture

skinparam componentStyle rectangle
' skinparam linetype ortho
skinparam shadowing false
skinparam defaultTextAlignment center
skinparam ArrowFontColor Black
skinparam ArrowFontSize 12
skinparam component {
    BackgroundColor #219cd3
    BorderColor #333
}

' Data Processing
package "Data Processing" [[https://test.com]] {
    component "       EGON       "
}

package "Deployment" {
    component "Ansible" [[https://test.com/]]
}

' Onboard systems
package "Onboard            Systems" {
    component "           AEROS           " [[https://example.com/aeros]]
    component "GLADOS" [[https://example.com/glados]]
}

' Ground systems
package "Ground Systems" {
    component "CANSATCOM" [[https://example.com/cansatcom]]
    component "CANSITE" [[https://example.com/cansite]]
}

' Relationships with clickable links
"Ansible" --> "           AEROS           "
"           AEROS           " <-left- "       EGON       " : [[https://test.com/ data/]]
"           AEROS           " <-down-> "GLADOS" 
"CANSATCOM" <-left-> "           AEROS           " :  [[https://example.com/xbee-protocol Methlink]]
"CANSATCOM" -down-> "CANSITE"

@enduml
