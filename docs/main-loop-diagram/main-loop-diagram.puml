@startuml Current AerOS Main Loop

skinparam BackgroundColor #FFFFFF

' Style the title
skinparam title {
    BackgroundColor #3a9bd3
    FontColor Black
    BorderRoundCorner 15
    FontSize 18
    FontStyle Bold
}

' Apply similar style to activities
skinparam activity {
    BackgroundColor #3a9bd3
    FontColor Black
    BorderRoundCorner 15
    BorderColor #3a9bd3
}

' Style arrows
skinparam Arrow {
    Color #3a9bd3
    FontColor Black
}

' Style notes (if used)
skinparam note {
    BackgroundColor #FFFFFF
    FontColor Black
    BorderColor #3a9bd3
}

title Current AerOS Main Loop

start
repeat
:send heartbeat;
note right: **Values**:\n - state \n - mission time
repeat while (valid event for state transition) is (no)
->yes;
:change state;
stop

@enduml