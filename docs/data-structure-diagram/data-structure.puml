@startuml
top to bottom direction
scale 600 width

skinparam backgroundColor transparent
skinparam noteBorderColor transparent
skinparam folder {
    BackgroundColor #FFFFFF
}
skinparam file {
    BackgroundColor #219cd3
}
skinparam noteBackgroundColor white
skinparam noteShadowing false

folder "data/" as data {

    folder "logs/" as logs {
        file "**aeros-YYYY-MM-DD_HH-MM-SS.log**" as logfile
    }

    folder "mission-YYYY-MM-DD_HH-MM-SS/" as mission {

        file "**system_stats.csv**\nindex | datetime | cpu_percent | memory_percent | cpu_temp | cpu_clock\nSpeicherbedarf: ~19.2MB" as sysstats

        file "**bme280.csv**\nindex | datetime | temperature | pressure | humidity | altitude | relative_altitude\nSpeicherbedarf: ~18.5MB" as bme280

        file "**bme390.csv**\nindex | datetime | temperature | pressure | humidity | altitude | relative_altitude\nSpeicherbedarf: ~18.5MB" as bme390

        file "**color_camera_YYYY-MM-DD_HH-MM-SS.mp4**\nCodec: H.264\nResolution: Nach Konfiguration\nFPS: config.color_camera.fps\nSpeicherbedarf: ~7.8GB" as video

        file "**thermal_YYYY-MM-DD_HH-MM-SS.mp4**\nCodec: H.264\nResolution: 256 x 192\nFPS: 20\nSpeicherbedarf: ~1.8GB" as thermal

        ' ---- layout control (must come AFTER declarations) ----
        sysstats -[hidden]-> bme280
        bme280  -[hidden]-> bme390
        bme390  -[hidden]-> video
        video   -[hidden]-> thermal
    }
}
@enduml