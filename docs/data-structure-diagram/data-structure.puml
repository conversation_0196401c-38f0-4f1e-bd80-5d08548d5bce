@startuml AerOS Data Structure

skinparam backgroundColor transparent
skinparam noteBorderColor transparent
skinparam folder {
    BackgroundColor #FFFFFF
}
skinparam file {
    BackgroundColor #219cd3
}
skinparam noteBackgroundColor white
skinparam noteShadowing false


folder "data/" as data {
    folder "logs/" as logs {
        file "**aeros-YYYY-MM-DD_HH-MM-SS.log**" as logfile 
    }
    
    folder "mission-YYYY-MM-DD_HH-MM-SS/" as mission {
        file "**system_stats.csv**\nindex | datetime | cpu_percent | memory_percent | cpu_temp | cpu_clock" as sysstats
        
        file "**thermal_camera.csv**\nindex | datetime | thermal_image" as thermal
        
        file "**bme280.csv**\nindex | datetime | temperature | pressure | humidity | altitude | relative_altitude" as bme280
        
        file "**color_camera_YYYY-MM-DD_HH-MM-SS.mp4**\nCodec: H.264\nResolution: config.color_camera.width x config.color_camera.height\nFPS: config.color_camera.fps" as video 
    }
}


@enduml
